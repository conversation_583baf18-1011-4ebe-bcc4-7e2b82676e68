/**
 * 冲突状态管理Slice
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../../store';
import { Conflict, ConflictStatus, ConflictResolutionStrategy } from '../../services/ConflictResolutionService';

// 冲突状态接口
export interface ConflictState {
  conflicts: Conflict[];
  selectedConflictId: string | null;
  showConflictPanel: boolean;
}

// 初始状态
const initialState: ConflictState = {
  conflicts: [],
  selectedConflictId: null,
  showConflictPanel: false
};

// 创建Slice
export const conflictSlice = createSlice({
  name: 'conflict',
  initialState,
  reducers: {
    // 添加冲突
    addConflict: (state, action: PayloadAction<Conflict>) => {
      // 检查是否已存在相同ID的冲突
      const existingIndex = state.conflicts.findIndex(c => c.id === action.payload.id);

      if (existingIndex >= 0) {
        // 更新现有冲突
        state.conflicts[existingIndex] = action.payload;
      } else {
        // 添加新冲突
        state.conflicts.push(action.payload);
      }

      // 只在编辑器页面自动显示冲突面板
      const currentPath = window.location.pathname;
      const isEditorPage = currentPath.includes('/editor/') ||
                          currentPath.includes('/terrain-editor') ||
                          currentPath.includes('/feedback-demo');
      const isProjectsPage = currentPath.includes('/projects') || currentPath === '/';

      if (isEditorPage && !isProjectsPage) {
        state.showConflictPanel = true;
      }

      // 如果没有选中的冲突，选中新添加的冲突
      if (state.selectedConflictId === null) {
        state.selectedConflictId = action.payload.id;
      }
    },
    
    // 解决冲突
    resolveConflict: (state, action: PayloadAction<{
      conflictId: string;
      status: ConflictStatus;
      resolution: ConflictResolutionStrategy;
      customResolution?: any;
    }>) => {
      const { conflictId, status, resolution, customResolution } = action.payload;
      
      // 查找冲突
      const conflictIndex = state.conflicts.findIndex(c => c.id === conflictId);
      
      if (conflictIndex >= 0) {
        // 更新冲突状态
        state.conflicts[conflictIndex] = {
          ...state.conflicts[conflictIndex],
          status,
          resolution,
          customResolution,
          resolvedAt: Date.now()
        };
      }
    },
    
    // 设置冲突列表
    setConflicts: (state, action: PayloadAction<Conflict[]>) => {
      state.conflicts = action.payload;
      
      // 如果选中的冲突不在列表中，清除选中
      if (state.selectedConflictId && !action.payload.some(c => c.id === state.selectedConflictId)) {
        state.selectedConflictId = null;
      }
    },
    
    // 选择冲突
    selectConflict: (state, action: PayloadAction<string | null>) => {
      state.selectedConflictId = action.payload;
    },
    
    // 显示/隐藏冲突面板
    setShowConflictPanel: (state, action: PayloadAction<boolean>) => {
      state.showConflictPanel = action.payload;
    },
    
    // 清除所有冲突
    clearConflicts: (state) => {
      state.conflicts = [];
      state.selectedConflictId = null;
    },
    
    // 清除已解决的冲突
    clearResolvedConflicts: (state) => {
      state.conflicts = state.conflicts.filter(c => c.status === ConflictStatus.PENDING);
      
      // 如果选中的冲突已被清除，清除选中
      if (state.selectedConflictId && !state.conflicts.some(c => c.id === state.selectedConflictId)) {
        state.selectedConflictId = null;
      }
    }
  }
});

// 导出Actions
export const {
  addConflict,
  resolveConflict,
  setConflicts,
  selectConflict,
  setShowConflictPanel,
  clearConflicts,
  clearResolvedConflicts
} = conflictSlice.actions;

// 选择器
export const selectConflicts = (state: RootState) => state.conflict.conflicts;
export const selectPendingConflicts = (state: RootState) => 
  state.conflict.conflicts.filter(c => c.status === ConflictStatus.PENDING);
export const selectResolvedConflicts = (state: RootState) => 
  state.conflict.conflicts.filter(c => c.status !== ConflictStatus.PENDING);
export const selectSelectedConflictId = (state: RootState) => state.conflict.selectedConflictId;
export const selectSelectedConflict = (state: RootState) => {
  const id = state.conflict.selectedConflictId;
  return id ? state.conflict.conflicts.find(c => c.id === id) || null : null;
};
export const selectShowConflictPanel = (state: RootState) => state.conflict.showConflictPanel;

// 导出Reducer
export default conflictSlice.reducer;
